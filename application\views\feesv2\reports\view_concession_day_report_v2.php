<?php include(APPPATH . 'views/feesv2/reports/components/report_header.php'); ?>

    <div class="card-body pt-1">
      
      <div class="col-md-12">
        <div class="row">
        
        <div class="col-md-2 form-group">
            <p for="fromdateId" class="control-label">Select Date range</p>
            <div id="reportrange"  >                                            
                    <span></span>
              <input type="hidden" id="from_date">
              <input type="hidden" id="to_date">
            </div>
        </div>

          <div class="col-md-2 form-group" id="multiBlueprintSelect">
            <p style="margin-bottom: 4px; margin-left:3px;">Select Fee Type</p>
            <select class="form-control select" id="blueprint_type" required="" name="fee_type">
              <option value=""><?php echo 'All Fee Type' ?></option>
              <?php foreach ($fee_blueprints as $key => $val) { ?>
                <option value="<?= $val->id ?>"><?php echo $val->name?></option>
              <?php } ?>
            </select>
          </div>


          <div class="col-md-2 form-group">
            <p style="margin-bottom: 4px; margin-left:3px;">Class</p>
            <?php 
                $array = array();
                foreach ($classes as $key => $class) {
                  $array[$class->classId] = $class->className; 
                }
            echo form_dropdown("class_name[]", $array, set_value("class_name"), "id='classId' multiple title='Select Classes' class='form-control classId select '");
            ?>
          </div>


           <div class="col-md-2 form-group">
            <p style="margin-bottom: 4px; margin-left:3px;">Select Class/Sections</p>
              <?php 
                $array = array();
                // $array[0] = 'All Section';
                foreach ($classSectionList as $key => $cl) {
                    $array[$cl->id] = $cl->class_name . $cl->section_name;
                }
                echo form_dropdown("classSectionId", $array, '', "id='classSectionId' multiple title='Select Class/Section' class='form-control select'");
              ?>
            </div>

            <div class="col-md-2 form-group" style="margin-top: 22px;">
              <input type="button" value="Get Report" id="getReport" class="btn btn-primary">
            </div>
        </div>
          <div class="col-12 text-center loading-icon" style="display: none;">
            <i class="fa fa-spinner fa-spin" style="font-size: 40px;"></i>
          </div>
          <div class="text-center"><div style="display: none;" class="progress" id="progress"><div id="progress-ind" class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" ariavaluenow="50" aria-valuemin="0" aria-valuemax="100" style="width: 50%"></div></div></div>

      </div>
      <div class="col-md-12 pt-2" style="overflow: hidden;" id="div_id">

        <div id="concession_day_data" class="fee_balance pt-3 table-responsive">
          
        </div>

      </div>
    </div>


  </div> 
</div>
<style type="text/css">
  #sliderDiv {
    text-align: center;
    width: 350px;
    float: right;
  }
  .table>thead>tr>th{
    white-space: nowrap;
  }
</style>

<script>
  $(document).ready(function(){

    $("#reportrange").daterangepicker({
      ranges: {
        'Today': [moment(), moment()],
        'Yesterday': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
        'Last 7 Days': [moment().subtract(6, 'days'), moment()],
        'Last 30 Days': [moment().subtract(29, 'days'), moment()],
        'This Month': [moment().startOf('month'), moment()],
        'Last Month': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')],
      },
      opens: 'right',
      buttonClasses: ['btn btn-default'],
        applyClass: 'btn-small btn-primary',
        cancelClass: 'btn-small',
        format: 'DD-MM-YYYY',
        separator: ' to ',
        startDate: moment().subtract(6, 'days'),
        endDate: moment()            
    },function(start, end) {
        $('#reportrange span').html(start.format('MMM D, YYYY') + ' - ' + end.format('MMM D, YYYY'));
        $('#from_date').val(start.format('DD-MM-YYYY'));
        $('#to_date').val(end.format('DD-MM-YYYY'));
    });

    $("#reportrange span").html(moment().subtract(6, 'days').format('MMM D, YYYY') + ' - ' + moment().format('MMM D, YYYY'));
    $('#from_date').val(moment().subtract(6, 'days').format('DD-MM-YYYY'));
    $('#to_date').val(moment().format('DD-MM-YYYY'));



    $('#getReport').on('click',function(){
      var fee_type = $('#blueprint_type').val();
      // if (fee_type =='') {
      //   return false;
      // }
      $('.loading-icon').show();
      $("#progress").show();
      $('#getReport').prop('disabled', true).val('Please wait...');
      $('.fee_balance').html('');
      $('.total_summary').html('');
      var classSectionId =  $('#classSectionId').val();
      var classId =  $('#classId').val();
      var from_date = $("#from_date").val();
      console.log(from_date);
	  var to_date = $("#to_date").val();

      $.ajax({
        url: '<?php echo site_url('feesv2/Reports/get_fee_concession_day_details'); ?>',
        data: {'fee_type':fee_type,'classSectionId':classSectionId,'classId':classId,'from_date':from_date,'to_date':to_date},
        type: "post",
        success: function (data) {
          $('.loading-icon').hide();
          $("#progress").hide();
          $('#getReport').prop('disabled', false).val('Get Report');
          var students = JSON.parse(data);
          // console.log(students);
          $("#concession_day_data").html(construct_table(students));

          const reportName=`Concession_day_report ${new Date().toLocaleString('default', { month: 'short' })+" "+new Date().getDate()+" "+new Date().getFullYear()}_${new Date().getHours()+""+new Date().getMinutes()}`;
          
          $('#concession_report_table').DataTable( {
            ordering:false,
            paging : false,
            scrollY :'40vh',
            "language": {
              "search": "",
              "searchPlaceholder": "Enter Search..."
            },
            dom: '<"dt-top-controls"fB>rtip',
            buttons: [
              {
              extend: 'excelHtml5',
              text: '<button class="btn btn-info"><span class="fa fa-file-excel-o" aria-hidden="true"></span> Excel</button>',
              filename: reportName,
              },
              {
              // extend: 'print',
              text: '<button class="btn btn-info"><span class="fa fa-print" aria-hidden="true"></span> Print</button>',
              action: function ( e, dt, node, config ) {
                customPrintConcession();
              }
              },
              {
              extend: 'pdfHtml5',
              text: '<button class="btn btn-info"><span class="fa fa-file-pdf-o" aria-hidden="true"></span> PDF</button>',
              filename: reportName,
              }
            ]
              });
        },
        error: function (err) {
          alert('Error');
          console.log(err);
        }
      });
    });
  });

  function customPrintConcession() {
    const printWindow = window.open('', '_blank');
    let printHeaderText = document.querySelector('.panel_title_new_style_staff')?.innerText || 'Concession Day Report';
    let summaryTable = document.querySelector('.total_summary')?.innerHTML || '';

    // Manually get thead and tbody and reconstruct the table
    let thead = $('#concession_report_table').closest('body').find('thead').first().prop('outerHTML');
    let tbody = $('#concession_report_table').closest('body').find('tbody').first().prop('outerHTML');
    let componentTable = '<h4>No data</h4>';
    if (thead && tbody) {
        componentTable = `<table style="width:100%;border-collapse:collapse;">${thead}${tbody}</table>`;
    }

    printWindow.document.write(`
        <html>
        <head>
            <title>Fee Concessions Report</title>
            <style>
            @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500&display=swap');

                body {
                    font-family: 'Poppins', sans-serif;
                    padding: 20px;
                }
                h2, h3, h4, h5 {
                    margin: 5px 0;
                    text-align: center;
                }
                table {
                    width: 100%;
                    border-collapse: collapse;
                    margin: 15px 0;
                    font-size: 11px;
                    background-color: #ffffff;
                    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.06);
                }
                th, td {
                    border: 1px solid #ddd;
                    padding: 10px 14px;
                    text-align: left;
                }
                thead th {
                    background-color: #f1f5f9;
                    color: #111827;
                    font-weight: 500;
                }
                #print_visible {
                    display: block !important;
                }
                @media print {
                    table { page-break-inside: auto; }
                    tr { page-break-inside: avoid; }
                }
            </style>
        </head>
        <body>
            <h2>${printHeaderText}</h2>
            ${summaryTable ? '<h3>Fee Summary</h3>' + summaryTable : ''}
            ${componentTable}
            <script>
            window.onload = function() {
                window.print();
            };
            window.onafterprint = function() {
                window.close();
            };
            <\/script>
        </body>
        </html>
    `);

    printWindow.document.close();
    printWindow.print();
}

  function construct_table(data){
    // console.log(data);
    var html ='';

  if (data.length == 0) {   
    html += '<h4>No data </h4>';
  }
  else{
      html+=`<table id="concession_report_table" class="table table-bordered">
            <thead>
              <tr style="white-space: nowrap">
                <th>#</th>
                <th>Fee Type</th>
                <th>Student Name</th>
                <th>Class/Section</th>
                <th>Created On</th>
                <th>Remarks</th>
                <th>Amount</th>
                <th>Created By</th>
              </tr>
            </thead>`;
      html += `<tbody>`;
      for (let i = 0; i < data.length; i++) {
        
      }
      for(var i=0;i<data.length;i++){
      // console.log(data_val);
      html+=`
              <tr>
                <td>${i+1}</td>
                <td>${data[i].fee_type}</td>
                <td>${data[i].student_name}</td>
                <td>${data[i].class_section}</td>
                <td>${data[i].create_date}</td>`;
                if(data[i].remarks){
                  html+=`<td>${data[i].remarks}</td>`;
                }else{
                  html+=`<td style="text-align: center;">-</td>`;
                }
                html+=`<td>${data[i].concession_amount}</td>
                <td>${data[i].created_name}</td>
                </tr>`;
      }
      html+=`</tbody>
        </table>`; 
    }
    return html;
  }


</script>

<style type="text/css">

</style>
<script type="text/javascript" src="<?php echo base_url('assets/js/plugins/moment.min.js') ?>"></script>
<script type="text/javascript" src="<?php echo base_url('assets/js/plugins/daterangepicker/daterangepicker.js') ?>"></script>

